using System.Reflection;
using FreeSql;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Autofac;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.Modularity;
using WASApp.Entity;
using Yitter.IdGenerator;

namespace WASApp;

[DependsOn(typeof(AbpAutofacModule),
    typeof(AbpBackgroundWorkersModule))]
public class AppModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        base.ConfigureServices(context);
        var configuration = context.Services.GetConfiguration();

        Func<IServiceProvider, IFreeSql> fsqlFactory = r =>
        {
            IFreeSql fsql = new FreeSql.FreeSqlBuilder()
                .UseConnectionString(FreeSql.DataType.PostgreSQL,
                    @"Host=*************;Port=5432;Username=root;Password=*******;Database=TronDB;ArrayNullabilityMode=Always;Pooling=true;Maximum Pool Size=2")
                .UseNameConvert(FreeSql.Internal.NameConvertType.ToLower)
                .UseMonitorCommand(cmd =>
                    Console.WriteLine($"[{DateTime.Now.ToString("HH:mm:ss")}] {cmd.CommandText}\r\n")) //监听SQL语句
                .UseNoneCommandParameter(true)
                .UseAutoSyncStructure(true) //自动同步实体结构到数据库，FreeSql不会扫描程序集，只有CRUD时才会生成表。
                .Build();
            YitIdHelper.SetIdGenerator(new IdGeneratorOptions(1) { WorkerIdBitLength = 6 });
            var serverTime = fsql.Ado.QuerySingle(() => DateTime.UtcNow);
            var timeOffset = DateTime.UtcNow.Subtract(serverTime);

            fsql.Aop.AuditValue += (_, e) =>
            {
                //数据库时间
                if ((e.Column.CsType == typeof(DateTime) || e.Column.CsType == typeof(DateTime?))
                    && e.Column.Attribute.ServerTime != DateTimeKind.Unspecified
                    && (e.Value == null || (DateTime)e.Value == default || (DateTime?)e.Value == default))
                {
                    e.Value = (e.Column.Attribute.ServerTime == DateTimeKind.Utc ? DateTime.UtcNow : DateTime.Now)
                        .Subtract(timeOffset);
                }

                //雪花Id
                if (e.Column.CsType == typeof(long)
                    && e.Property.GetCustomAttribute<SnowflakeAttribute>(false) != null
                    && (e.Value == null || (long)e.Value == default || (long?)e.Value == default))
                {
                    e.Value = YitIdHelper.NextId();
                }

                //if (user == null || user.Id <= 0)
                //{
                //    return;
                //}

                //if (e.AuditValueType is AuditValueType.Insert or AuditValueType.InsertOrUpdate)
                //{
                //    switch (e.Property.Name)
                //    {
                //        case "CreatedUserId":
                //        case "OwnerId":
                //        case "MemberId":
                //            if (e.Value == null || (long)e.Value == default || (long?)e.Value == default)
                //            {
                //                e.Value = user.Id;
                //            }
                //            break;

                //        case "CreatedUserName":
                //            if (e.Value == null || ((string)e.Value).IsNull())
                //            {
                //                e.Value = user.UserName;
                //            }
                //            break;
                //    }
                //}

                //if (e.AuditValueType is AuditValueType.Update or AuditValueType.InsertOrUpdate)
                //{
                //    switch (e.Property.Name)
                //    {
                //        case "ModifiedUserId":
                //            e.Value = user.Id;
                //            break;

                //        case "ModifiedUserName":
                //            e.Value = user.UserName;
                //            break;
                //    }
                //}
            };
            if (fsql.Select<MenuEntity>().Any() == false)
            {
                var repo = fsql.GetRepository<MenuEntity>();
                repo.DbContextOptions.EnableCascadeSave = true;
                repo.Insert(new[]
                {
                    new MenuEntity
                    {
                        Label = "通用管理",
                        ParentId = 0,
                        Path = "",
                        Sort = 100,
                        TargetBlank = false,
                        Icon = "",
                        Type = MenuEntityType.菜单,
                        Childs = new List<MenuEntity>
                        {
                            new MenuEntity
                            {
                                Label = "菜单管理",
                                Path = "Admin/Menu",
                                Sort = 101,
                                TargetBlank = false,
                                Icon = "",
                                Type = MenuEntityType.菜单,
                            },
                            new MenuEntity
                            {
                                Label = "角色管理",
                                Path = "Admin/Role",
                                Sort = 102,
                                TargetBlank = false,
                                Icon = "",
                                Type = MenuEntityType.菜单,
                            },
                            new MenuEntity
                            {
                                Label = "用户管理",
                                Path = "Admin/User",
                                Sort = 103,
                                TargetBlank = false,
                                Icon = "",
                                Type = MenuEntityType.菜单,
                            }
                        }
                    },
                });
            }

            return fsql;
        };

        context.Services.AddSingleton(fsqlFactory);
        context.Services.AddScoped<UnitOfWorkManager>();
        //context.Services.AddFreeRepository(null, typeof(Program).Assembly);
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        base.OnApplicationInitialization(context);


        var _logger = context.ServiceProvider.GetRequiredService<ILogger<AppModule>>();
        var hostEnvironment = context.ServiceProvider.GetRequiredService<IHostEnvironment>();
        _logger.LogDebug($"MonitorServerModule 加载成功=>EnvironmentName => {hostEnvironment.EnvironmentName}");
        
        var fsql=context.ServiceProvider.GetRequiredService<IFreeSql>();
        
        var menu=fsql.Select<MenuEntity>().ToList();
        Console.WriteLine(menu);
        
    }
}