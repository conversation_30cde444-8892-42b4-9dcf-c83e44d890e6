// See https://aka.ms/new-console-template for more information

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using Volo.Abp;
using WASApp;

var template = "{Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}";
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .MinimumLevel.Override("System", LogEventLevel.Error)
    .Enrich.FromLogContext()
    .WriteTo.Async(c => c.File($"{Environment.CurrentDirectory}/logs/.txt", 
        rollingInterval: RollingInterval.Hour, 
        outputTemplate: template))
    .WriteTo.Async(c => c.Console())
    .CreateLogger();

var builder = Host.CreateApplicationBuilder(args);

builder.Configuration.AddAppSettingsSecretsJson();
builder.Logging.ClearProviders().AddSerilog();
builder.ConfigureContainer(builder.Services.AddAutofacServiceProviderFactory());
//注入服务
//builder.Services.AddHostedService<MyHostService>();
//注入abp
await builder.Services.AddApplicationAsync<AppModule>();

var host = builder.Build();

await host.InitializeAsync();

await host.RunAsync();