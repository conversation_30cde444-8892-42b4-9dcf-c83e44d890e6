<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="FreeSql.Provider.PostgreSQL" Version="3.5.209" />
        <PackageReference Include="FreeSql.Repository" Version="3.5.209" />
        <PackageReference Include="Volo.Abp" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.AspNetCore.SignalR" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Autofac" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.BackgroundJobs" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.BackgroundWorkers" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Swashbuckle" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Validation" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Autofac" Version="8.3.4" />
        <PackageReference Include="Volo.Abp.Caching" Version="8.3.4" />
        <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="Yitter.IdGenerator" Version="1.0.14" />
    </ItemGroup>
</Project>
